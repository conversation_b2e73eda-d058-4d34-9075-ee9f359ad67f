/**
 * API Endpoints for the CashyMall mobile app
 */



export interface GetPagedParams {
  page?: number;
  size?: number;
  sortBy?: string;
  direction?: 'asc' | 'desc';
}


// Base API URL
export const API_BASE_URL = 'https://api.cashymall.com/api/v1';

// Authentication endpoints
export const AUTH_ENDPOINTS = {
  LOGIN: `${API_BASE_URL}/auth/authenticate`,
  SIGNUP: `${API_BASE_URL}/auth/register`,
  LOGOUT: `${API_BASE_URL}/auth/logout`,
  REFRESH_TOKEN: `${API_BASE_URL}/auth/refresh-token`,
  FORGOT_PASSWORD: `${API_BASE_URL}/auth/forgot-password`,
  RESET_PASSWORD: `${API_BASE_URL}/auth/reset-password`,
};

// User endpoints
export const USER_ENDPOINTS = {
  PROFILE: `${API_BASE_URL}/users/user`,
  UPDATE_PROFILE: `${API_BASE_URL}/users/update`,
  UPDATE_MY_PROFILE: `${API_BASE_URL}/users/update-my-profile`, // Alternative endpoint
  uploadImg: `${API_BASE_URL}/users/upload-profile-picture`,
  updatePassword: `${API_BASE_URL}/users/change-my-password`,

  UPDATE_ADDRESS: `${API_BASE_URL}/users/update-my-address`,

};

// Product endpoints
export const PRODUCT_ENDPOINTS = {
  LIST: `${API_BASE_URL}/products`,
  DETAILS: `${API_BASE_URL}/products`,
  SEARCH: `${API_BASE_URL}/products/search`,
  CATEGORIES: `${API_BASE_URL}/products/categories`,
  BRANDS: `${API_BASE_URL}/products/brand`,
  sale: `${API_BASE_URL}/products/discounted/paged`
};

// Category endpoints
export const CATEGORY_ENDPOINTS = {
  ROOT: `${API_BASE_URL}/categories/root`,
  DETAILS: `${API_BASE_URL}/categories`,
  SUBCATEGORIES: `${API_BASE_URL}/categories/subcategories`,

};

// Brand endpoints
export const BRAND_ENDPOINTS = {
  LIST: `${API_BASE_URL}/brands`,
  DETAILS: `${API_BASE_URL}/brands/details`,
};

// Cart endpoints
export const CART_ENDPOINTS = {
  GET: `${API_BASE_URL}/cart`,
  ADD: `${API_BASE_URL}/cart/add`,
  UPDATE: `${API_BASE_URL}/cart/items`,
  REMOVE: `${API_BASE_URL}/cart/items`,
  CLEAR: `${API_BASE_URL}/cart/clear`,
};

// Wishlist endpoints
export const WISHLIST_ENDPOINTS = {
  GET: `${API_BASE_URL}/wishlist`,
  ADD: `${API_BASE_URL}/wishlist/add`,
  REMOVE: `${API_BASE_URL}/wishlist/remove`,
  check: `${API_BASE_URL}/wishlist/check`,
  clear: `${API_BASE_URL}/wishlist/clear`,
};

// Order endpoints
export const ORDER_ENDPOINTS = {
  LIST: `${API_BASE_URL}/orders`,
  DETAILS: `${API_BASE_URL}/orders`,
  CREATE: `${API_BASE_URL}/orders`,
  CANCEL: `${API_BASE_URL}/orders/cancel`,
  MYLIST: `${API_BASE_URL}/orders/customer`,

};

// Payment endpoints
export const PAYMENT_ENDPOINTS = {
  METHODS: `${API_BASE_URL}/payment/methods`,
  ADD_METHOD: `${API_BASE_URL}/payment/methods/add`,
  REMOVE_METHOD: `${API_BASE_URL}/payment/methods/remove`,
  PROCESS: `${API_BASE_URL}/payment/process`,
};

// Review endpoints
export const REVIEW_ENDPOINTS = {
  GET: `${API_BASE_URL}/reviews`,
  GETPaged: `${API_BASE_URL}/reviews/product`,
  ADD: `${API_BASE_URL}/reviews`,
  UPDATE: `${API_BASE_URL}/reviews`,
  DELETE: `${API_BASE_URL}/reviews`,
  PRODUCT_REVIEWS: `${API_BASE_URL}/reviews/product`,
  AVERAGE_RATING: `${API_BASE_URL}/reviews/product`,
  RATING_COUNT: `${API_BASE_URL}/reviews/product`,
  PAGED_REVIEWS: `${API_BASE_URL}/reviews/product`,
};

