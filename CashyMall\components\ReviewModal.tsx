import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  useColorScheme,
  TextInput,
  ScrollView,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';
import Button from './Button';
import { addReview } from '../services/api/review';
import { ReviewRequest } from '../types/product';

interface ReviewModalProps {
  visible: boolean;
  onClose: () => void;
  productId: number;
  productName: string;
  onSuccess?: () => void;
}

export default function ReviewModal({
  visible,
  onClose,
  productId,
  productName,
  onSuccess
}: ReviewModalProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  const [rating, setRating] = useState(0);
  const [title, setTitle] = useState('');
  const [comment, setComment] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    if (rating === 0) {
      Alert.alert('Rating Required', 'Please select a rating before submitting your review.');
      return;
    }

    if (title.trim().length === 0) {
      Alert.alert('Title Required', 'Please enter a title for your review.');
      return;
    }

    if (comment.trim().length === 0) {
      Alert.alert('Comment Required', 'Please enter a comment for your review.');
      return;
    }

    setIsLoading(true);
    try {
      const reviewData: ReviewRequest = {
        productId,
        rating,
        title: title.trim(),
        comment: comment.trim()
      };

      const response = await addReview(reviewData);
      
      if (response.error) {
        Alert.alert('Error', response.error || 'Failed to submit review');
        return;
      }

      Alert.alert('Success', 'Your review has been submitted successfully!');
      
      // Reset form
      setRating(0);
      setTitle('');
      setComment('');
      
      onSuccess && onSuccess();
      onClose();
    } catch (error) {
      console.error('Error submitting review:', error);
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setRating(0);
    setTitle('');
    setComment('');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Write a Review
          </Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Product Name */}
          <Text style={[styles.productName, { color: colors.text }]}>
            {productName}
          </Text>

          {/* Rating Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Rating *
            </Text>
            <View style={styles.ratingContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <TouchableOpacity
                  key={star}
                  onPress={() => setRating(star)}
                  style={styles.starButton}
                >
                  <Ionicons
                    name={star <= rating ? "star" : "star-outline"}
                    size={32}
                    color={star <= rating ? "#FFD700" : colors.tabIconDefault}
                  />
                </TouchableOpacity>
              ))}
            </View>
            <Text style={[styles.ratingText, { color: colors.tabIconDefault }]}>
              {rating === 0 ? 'Tap to rate' : 
               rating === 1 ? 'Poor' :
               rating === 2 ? 'Fair' :
               rating === 3 ? 'Good' :
               rating === 4 ? 'Very Good' : 'Excellent'}
            </Text>
          </View>

          {/* Title Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Review Title *
            </Text>
            <TextInput
              style={[
                styles.titleInput,
                { 
                  borderColor: colors.border,
                  backgroundColor: colors.card,
                  color: colors.text
                }
              ]}
              placeholder="Summarize your experience"
              placeholderTextColor={colors.tabIconDefault}
              value={title}
              onChangeText={setTitle}
              maxLength={100}
            />
            <Text style={[styles.characterCount, { color: colors.tabIconDefault }]}>
              {title.length}/100
            </Text>
          </View>

          {/* Comment Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Your Review *
            </Text>
            <TextInput
              style={[
                styles.commentInput,
                { 
                  borderColor: colors.border,
                  backgroundColor: colors.card,
                  color: colors.text
                }
              ]}
              placeholder="Tell others about your experience with this product..."
              placeholderTextColor={colors.tabIconDefault}
              value={comment}
              onChangeText={setComment}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
              maxLength={500}
            />
            <Text style={[styles.characterCount, { color: colors.tabIconDefault }]}>
              {comment.length}/500
            </Text>
          </View>
        </ScrollView>

        {/* Submit Button */}
        <View style={[styles.footer, { borderTopColor: colors.border }]}>
          <Button
            title={isLoading ? "Submitting..." : "Submit Review"}
            onPress={handleSubmit}
            disabled={isLoading || rating === 0 || title.trim().length === 0 || comment.trim().length === 0}
            style={styles.submitButton}
          />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: Layout.spacing.md,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: Layout.spacing.lg,
    textAlign: 'center',
  },
  section: {
    marginBottom: Layout.spacing.lg,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: Layout.spacing.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: Layout.spacing.xs,
  },
  starButton: {
    padding: Layout.spacing.xs,
  },
  ratingText: {
    textAlign: 'center',
    fontSize: 14,
  },
  titleInput: {
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.sm,
    fontSize: 16,
    marginBottom: Layout.spacing.xs,
  },
  commentInput: {
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.sm,
    fontSize: 16,
    height: 120,
    marginBottom: Layout.spacing.xs,
  },
  characterCount: {
    fontSize: 12,
    textAlign: 'right',
  },
  footer: {
    padding: Layout.spacing.md,
    borderTopWidth: 1,
  },
  submitButton: {
    width: '100%',
  },
});
