/**
 * Review API service for the CashyMall mobile app
 */
import { get, post, put, del } from './index';
import { REVIEW_ENDPOINTS, API_BASE_URL } from './endpoints';
import { getToken } from '../storage/tokenStorage';
import {
  Review,
  ReviewRequest,
  ReviewResponse,
  ProductReviewDTO,
  ProductReviewCreateDTO,
  ServiceResponse
} from '../../types/product';

/**
 * Get reviews for a specific product
 * @param productId Product ID
 * @returns List of reviews for the product
 */
export async function getProductReviews(productId: number | string) {
  const response = await get<ReviewResponse>(`${REVIEW_ENDPOINTS.PRODUCT_REVIEWS}/${productId}`);
  return response;
}

/**
 * Add a new review for a product (legacy function)
 * @param reviewData Review data
 * @returns API response
 */
export async function addReview(reviewData: ReviewRequest) {
  const response = await post<Review>(REVIEW_ENDPOINTS.ADD, reviewData);
  return response;
}

/**
 * Update an existing review (legacy function)
 * @param reviewId Review ID
 * @param reviewData Updated review data
 * @returns API response
 */
export async function updateReview(reviewId: number, reviewData: Partial<ReviewRequest>) {
  const response = await put<Review>(`${REVIEW_ENDPOINTS.UPDATE}/${reviewId}`, reviewData);
  return response;
}

/**
 * Delete a review (legacy function)
 * @param reviewId Review ID
 * @returns API response
 */
export async function deleteReview(reviewId: number) {
  const response = await del(`${REVIEW_ENDPOINTS.DELETE}/${reviewId}`);
  return response;
}

/**
 * Get all reviews (admin/user specific)
 * @returns List of all reviews
 */
export async function getAllReviews() {
  const response = await get<ReviewResponse>(REVIEW_ENDPOINTS.GET);
  return response;
}

// ===== NEW FUNCTIONS MATCHING WEB VERSION =====

/**
 * Create a new review (matches web version)
 * @param review Review data
 * @returns API response
 */
export async function createReview(review: ProductReviewCreateDTO): Promise<ServiceResponse<ProductReviewDTO>> {
  try {
    const authData = await getToken();
    const token = authData?.token;

    const response = await fetch(`${API_BASE_URL}/reviews`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ""
      },
      body: JSON.stringify(review)
    });

    if (!response.ok) {
      let errorMessage;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || `Status ${response.status}`;
      } catch(e) {
        errorMessage = `Server returned ${response.status} ${response.statusText}`;
      }

      throw new Error(`Failed to create review: ${errorMessage}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating review:', error);
    throw error;
  }
}

/**
 * Update a review (matches web version)
 * @param reviewId Review ID
 * @param review Updated review data
 * @returns API response
 */
export async function updateReviewNew(
  reviewId: number,
  review: ProductReviewCreateDTO
): Promise<ServiceResponse<ProductReviewDTO>> {
  try {
    const authData = await getToken();
    const token = authData?.token;

    const response = await fetch(`${API_BASE_URL}/reviews/${reviewId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ""
      },
      body: JSON.stringify(review)
    });

    if (!response.ok) {
      let errorMessage;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || `Status ${response.status}`;
      } catch(e) {
        errorMessage = `Server returned ${response.status} ${response.statusText}`;
      }

      throw new Error(`Failed to update review: ${errorMessage}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating review:', error);
    throw error;
  }
}

/**
 * Delete a review (matches web version)
 * @param reviewId Review ID
 * @returns API response
 */
export async function deleteReviewNew(reviewId: number): Promise<ServiceResponse<boolean>> {
  try {
    const authData = await getToken();
    const token = authData?.token;

    const response = await fetch(`${API_BASE_URL}/reviews/${reviewId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ""
      }
    });

    if (!response.ok) {
      let errorMessage;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || `Status ${response.status}`;
      } catch(e) {
        errorMessage = `Server returned ${response.status} ${response.statusText}`;
      }

      throw new Error(`Failed to delete review: ${errorMessage}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error deleting review:', error);
    throw error;
  }
}

/**
 * Get average rating for a product (matches web version)
 * @param productId Product ID
 * @returns Average rating
 */
export async function getAverageRating(productId: number): Promise<ServiceResponse<number>> {
  try {
    const authData = await getToken();
    const token = authData?.token;

    const response = await fetch(`${API_BASE_URL}/reviews/product/${productId}/average-rating`, {
      headers: {
        "Content-Type": "application/json",
        "Authorization": token ? `Bearer ${token}` : ""
      }
    });

    if (!response.ok) {
      let errorMessage;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || `Status ${response.status}`;
      } catch(e) {
        errorMessage = `Server returned ${response.status} ${response.statusText}`;
      }

      throw new Error(`Failed to fetch average rating: ${errorMessage}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching average rating:', error);
    throw error;
  }
}

/**
 * Get rating count by rating value (matches web version)
 * @param productId Product ID
 * @param rating Optional rating value to filter by
 * @returns Rating count
 */
export async function getRatingCount(
  productId: number,
  rating?: number
): Promise<ServiceResponse<number>> {
  try {
    const authData = await getToken();
    const token = authData?.token;

    const url = new URL(`${API_BASE_URL}/reviews/product/${productId}/rating-count`);
    if (rating !== undefined) {
      url.searchParams.append('rating', rating.toString());
    }

    const response = await fetch(url.toString(), {
      headers: {
        "Content-Type": "application/json",
        "Authorization": token ? `Bearer ${token}` : ""
      }
    });

    if (!response.ok) {
      let errorMessage;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || `Status ${response.status}`;
      } catch(e) {
        errorMessage = `Server returned ${response.status} ${response.statusText}`;
      }

      throw new Error(`Failed to fetch rating count: ${errorMessage}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching rating count:', error);
    throw error;
  }
}

/**
 * Get paged reviews by product (matches web version)
 * @param productId Product ID
 * @param page Page number (default: 0)
 * @param size Page size (default: 10)
 * @param sortBy Sort field (default: 'createdAt')
 * @param direction Sort direction (default: 'desc')
 * @returns Paged reviews
 */
export async function getPagedReviewsByProduct(
  productId: number,
  page = 0,
  size = 10,
  sortBy = 'createdAt',
  direction = 'desc'
): Promise<ServiceResponse<{ content: ProductReviewDTO[], totalElements: number }>> {
  try {
    const authData = await getToken();
    const token = authData?.token;

    const url = new URL(`${API_BASE_URL}/reviews/product/${productId}/paged`);
    url.searchParams.append('page', page.toString());
    url.searchParams.append('size', size.toString());
    url.searchParams.append('sortBy', sortBy);
    url.searchParams.append('direction', direction);

    const response = await fetch(url.toString(), {
      headers: {
        "Content-Type": "application/json",
        "Authorization": token ? `Bearer ${token}` : ""
      }
    });

    if (!response.ok) {
      let errorMessage;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || `Status ${response.status}`;
      } catch(e) {
        errorMessage = `Server returned ${response.status} ${response.statusText}`;
      }

      throw new Error(`Failed to fetch paged reviews: ${errorMessage}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching paged reviews:', error);
    throw error;
  }
}
