/**
 * Review API service for the CashyMall mobile app
 */
import { get, post, put, del } from './index';
import { REVIEW_ENDPOINTS } from './endpoints';
import { Review, ReviewRequest, ReviewResponse } from '../../types/product';

/**
 * Get reviews for a specific product
 * @param productId Product ID
 * @returns List of reviews for the product
 */
export async function getProductReviews(productId: number | string) {
  const response = await get<ReviewResponse>(`${REVIEW_ENDPOINTS.PRODUCT_REVIEWS}/${productId}`);
  return response;
}

/**
 * Add a new review for a product
 * @param reviewData Review data
 * @returns API response
 */
export async function addReview(reviewData: ReviewRequest) {
  const response = await post<Review>(REVIEW_ENDPOINTS.ADD, reviewData);
  return response;
}

/**
 * Update an existing review
 * @param reviewId Review ID
 * @param reviewData Updated review data
 * @returns API response
 */
export async function updateReview(reviewId: number, reviewData: Partial<ReviewRequest>) {
  const response = await put<Review>(`${REVIEW_ENDPOINTS.UPDATE}/${reviewId}`, reviewData);
  return response;
}

/**
 * Delete a review
 * @param reviewId Review ID
 * @returns API response
 */
export async function deleteReview(reviewId: number) {
  const response = await del(`${REVIEW_ENDPOINTS.DELETE}/${reviewId}`);
  return response;
}

/**
 * Get all reviews (admin/user specific)
 * @returns List of all reviews
 */
export async function getAllReviews() {
  const response = await get<ReviewResponse>(REVIEW_ENDPOINTS.GET);
  return response;
}
