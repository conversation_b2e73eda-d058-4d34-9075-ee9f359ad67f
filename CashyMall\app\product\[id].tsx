import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  useColorScheme,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Header from '../../components/Header';
import Button from '../../components/Button';
import ProductSelectionModal from '../../components/ProductSelectionModal';
import ReviewModal from '../../components/ReviewModal';
import ReviewList from '../../components/ReviewList';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { getProductDetails } from '../../services/api/product';
import { addToCart, checkItemExistsInCart } from '../../services/api/cart';
import { getProductReviews } from '../../services/api/review';
import { Product, Review } from '../../types/product';
import { useCartWishlist } from '../../context/CartWishlistContext';

export default function ProductDetailScreen() {
  const { id } = useLocalSearchParams();
  const productId = typeof id === 'string' ? id : '1';

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Review states
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);

  // Product selection modal state
  const [showSelectionModal, setShowSelectionModal] = useState(false);

  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  // Use context for real-time updates
  const { addToCartCount } = useCartWishlist();

  // Fetch product details from API
  useEffect(() => {
    const fetchProductDetails = async () => {
      setLoading(true);
      try {
        const response = await getProductDetails(productId);
        console.log('Product Details Response:', JSON.stringify(response, null, 2));

        // Handle different response formats
        let productData = null;

        if (response.data?.data) {
          // Nested data structure: { data: { data: Product } }
          productData = response.data.data;
        } else if (response.data && typeof response.data === 'object' && !Array.isArray(response.data)) {
          // Direct data structure: { data: Product }
          productData = response.data;
        } else if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          // Array structure: { data: [Product] }
          productData = response.data[0];
        }

        if (productData) {
          setProduct(productData);
          setError(null);
          setSelectedImageIndex(0); // Reset image selection when new product loads
          console.log('Product loaded successfully:', productData.name);
        } else {
          setError('Product not found or invalid format received from API');
          console.error('Invalid product details format:', response);
        }
      } catch (err) {
        setError('An error occurred while fetching product details');
        console.error('Product fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProductDetails();
  }, [productId]);

  // Fetch product reviews
  useEffect(() => {
    const fetchReviews = async () => {
      if (!product) return;

      setReviewsLoading(true);
      try {
        const response = await getProductReviews(product.id);
        console.log('Reviews Response:', response);

        if (response.data && Array.isArray(response.data)) {
          setReviews(response.data);
        } else {
          setReviews([]);
        }
      } catch (error) {
        console.error('Error fetching reviews:', error);
        setReviews([]);
      } finally {
        setReviewsLoading(false);
      }
    };

    fetchReviews();
  }, [product]);

  const handleIncrement = () => {
    setQuantity(quantity + 1);
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  // Check if product has selectable options (sizes or colors)
  const hasSelectableOptions = () => {
    if (!product) return false;
    const hasSizes = product.sizes && product.sizes.trim().length > 0;
    const hasColors = product.colors && product.colors.trim().length > 0;
    return hasSizes || hasColors;
  };

  // Handle add to cart - show selection modal if product has options
  const handleAddToCartClick = () => {
    if (hasSelectableOptions()) {
      setShowSelectionModal(true);
    } else {
      handleAddToCart();
    }
  };

  // Handle review modal success
  const handleReviewSuccess = () => {
    // Refresh reviews after adding a new one
    if (product) {
      const fetchReviews = async () => {
        try {
          const response = await getProductReviews(product.id);
          if (response.data && Array.isArray(response.data)) {
            setReviews(response.data);
          }
        } catch (error) {
          console.error('Error refreshing reviews:', error);
        }
      };
      fetchReviews();
    }
  };

  const handleAddToCart = async () => {
    if (!product) return;

    try {
      // Check if item already exists in cart (without size/color since this is basic add to cart)
      const itemExists = await checkItemExistsInCart(product.id.toString());

      if (itemExists) {
        // Show "already in cart" message and don't add the item
        Alert.alert('Already in Cart', 'This item is already in your cart.');
        return;
      }

      const response = await addToCart(product.id.toString(), quantity);
      if (response.error) {
        Alert.alert('Error', 'Failed to add item to cart');
        return;
      }

      // Update cart count immediately with the selected quantity
      addToCartCount(quantity);
      Alert.alert('Success', 'Item added to cart');
    } catch (err) {
      console.error('Error adding to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  // Show loading state
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header showBack />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading product details...
          </Text>
        </View>
      </View>
    );
  }

  // Show error state
  if (error || !product) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header showBack />
        <View style={styles.notFoundContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={[styles.notFoundText, { color: colors.text }]}>
            {error || 'Product not found'}
          </Text>
          <Button
            title="Go Back to Shop"
            onPress={() => router.push('/(tabs)/shop')}
            style={styles.notFoundButton}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header showBack />

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Product Images */}
        <View style={styles.imageContainer}>
          <Image
            source={{
              uri: selectedImageIndex === 0
                ? product.imageUrl
                : product.additionalImages?.[selectedImageIndex - 1] || product.imageUrl
            }}
            style={styles.mainImage}
            resizeMode="cover"
          />

          <View style={styles.thumbnailsContainer}>
            {/* Main image */}
            <TouchableOpacity
              key="main"
              style={[
                styles.thumbnailButton,
                selectedImageIndex === 0 && { borderColor: colors.primary }
              ]}
              onPress={() => setSelectedImageIndex(0)}
            >
              <Image
                source={{ uri: product.imageUrl }}
                style={styles.thumbnailImage}
                resizeMode="cover"
              />
            </TouchableOpacity>

            {/* Additional images if available */}
            {product.additionalImages && Array.isArray(product.additionalImages) && product.additionalImages.map((image, index) => (
              <TouchableOpacity
                key={index + 1}
                style={[
                  styles.thumbnailButton,
                  selectedImageIndex === index + 1 && { borderColor: colors.primary }
                ]}
                onPress={() => setSelectedImageIndex(index + 1)}
              >
                <Image
                  source={{ uri: image }}
                  style={styles.thumbnailImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Product Info */}
        <View style={styles.infoContainer}>
          {/* Product Name and Basic Info */}
          <Text style={[styles.productName, { color: colors.text }]}>
            {product.name}
          </Text>

          {/* Brand and Category */}
          <View style={styles.brandCategoryContainer}>
            <Text style={[styles.brandText, { color: colors.primary }]}>
              {product.brandName}
            </Text>
            <Text style={[styles.categoryText, { color: colors.tabIconDefault }]}>
              {product.categoryName}
              {product.subcategories && product.subcategories.length > 0 &&
                ` • ${product.subcategories.map(sub => sub.name).join(', ')}`
              }
            </Text>
          </View>

          {/* Rating and Reviews */}
          <View style={styles.ratingContainer}>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Ionicons
                  key={star}
                  name={star <= Math.floor(product.averageRating || 0) ? "star" : star <= (product.averageRating || 0) ? "star-half" : "star-outline"}
                  size={16}
                  color="#FFD700"
                  style={styles.starIcon}
                />
              ))}
            </View>
            <Text style={[styles.reviewCount, { color: colors.tabIconDefault }]}>
              ({product.reviewCount || 0} reviews)
            </Text>
          </View>

          {/* Price */}
          <View style={styles.priceContainer}>
            {product.discountPrice && product.discountPrice > 0 ? (
              <>
                <Text style={[styles.discountPrice, { color: colors.primary }]}>
                  ${(product.price - (product.price * product.discountPrice / 100)).toFixed(2)}
                </Text>
                <Text style={[styles.originalPrice, { color: colors.tabIconDefault }]}>
                  ${product.price.toFixed(2)}
                </Text>
                <View style={[styles.discountBadge, { backgroundColor: colors.primary }]}>
                  <Text style={styles.discountBadgeText}>
                    {product.discountPrice}% OFF
                  </Text>
                </View>
              </>
            ) : (
              <Text style={[styles.price, { color: colors.text }]}>
                ${product.price.toFixed(2)}
              </Text>
            )}
          </View>

          {/* Free Shipping Badge */}
          {product.freeShipping && (
            <View style={[styles.freeShippingBadge, { backgroundColor: colors.primary }]}>
              <Ionicons name="car-outline" size={16} color="#ffffff" />
              <Text style={styles.freeShippingText}>Free Shipping</Text>
            </View>
          )}

          {/* Stock Status */}
          <View style={styles.stockContainer}>
            <Ionicons
              name={product.stockLevel > 10 ? "checkmark-circle" : product.stockLevel > 0 ? "warning" : "close-circle"}
              size={16}
              color={product.stockLevel > 10 ? "#4CAF50" : product.stockLevel > 0 ? "#FF9800" : "#F44336"}
            />
            <Text style={[styles.stockText, {
              color: product.stockLevel > 10 ? "#4CAF50" : product.stockLevel > 0 ? "#FF9800" : "#F44336"
            }]}>
              {product.stockLevel > 10 ? `In Stock (${product.stockLevel} available)` :
               product.stockLevel > 0 ? `Low Stock (${product.stockLevel} left)` :
               'Out of Stock'}
            </Text>
          </View>

          {/* Description */}
          {product.description && product.description.trim() && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Description</Text>
              <Text style={[styles.description, { color: colors.text }]}>
                {product.description.trim()}
              </Text>
            </View>
          )}

          {/* Available Sizes */}
          {product.sizes && product.sizes.trim() && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Available Sizes</Text>
              <View style={styles.sizesContainer}>
                {product.sizes.split(',').map((size, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.sizeChip, { borderColor: colors.border }]}
                    onPress={() => setShowSelectionModal(true)}
                  >
                    <Text style={[styles.sizeText, { color: colors.text }]}>
                      {size.trim()}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <Text style={[styles.selectionHint, { color: colors.tabIconDefault }]}>
                Tap "Add to Cart" to select size and quantity
              </Text>
            </View>
          )}

          {/* Available Colors */}
          {product.colors && product.colors.trim() && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Available Colors</Text>
              <View style={styles.colorsContainer}>
                {product.colors.split(',').map((color, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.colorChip, { borderColor: colors.border }]}
                    onPress={() => setShowSelectionModal(true)}
                  >
                    <Text style={[styles.colorText, { color: colors.text }]}>
                      {color.trim()}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <Text style={[styles.selectionHint, { color: colors.tabIconDefault }]}>
                Tap "Add to Cart" to select color and quantity
              </Text>
            </View>
          )}

          {/* Features */}
          {product.features && product.features.trim() && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Key Features</Text>
              <View style={styles.featuresList}>
                {product.features.split(',').map((feature, index) => (
                  <View key={index} style={styles.featureItem}>
                    <Ionicons name="checkmark-circle" size={18} color={colors.primary} />
                    <Text style={[styles.featureText, { color: colors.text }]}>
                      {feature.trim()}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Product Details */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Product Details</Text>
            <View style={styles.detailsContainer}>

              {/* SKU */}
              {product.sku && product.sku.trim() && (
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: colors.tabIconDefault }]}>SKU:</Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>{product.sku.trim()}</Text>
                </View>
              )}

              {/* Barcode */}
              {product.barcode && product.barcode.trim() && (
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: colors.tabIconDefault }]}>Barcode:</Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>{product.barcode.trim()}</Text>
                </View>
              )}

              {/* Producer */}
              {product.producer && product.producer.trim() && (
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: colors.tabIconDefault }]}>Producer:</Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>{product.producer.trim()}</Text>
                </View>
              )}

              {/* Material */}
              {product.material && product.material.trim() && (
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: colors.tabIconDefault }]}>Material:</Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>{product.material.trim()}</Text>
                </View>
              )}

              {/* Fit */}
              {product.fit && product.fit.trim() && (
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: colors.tabIconDefault }]}>Fit:</Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>{product.fit.trim()}</Text>
                </View>
              )}

              {/* Origin */}
              {product.origin && product.origin.trim() && (
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: colors.tabIconDefault }]}>Origin:</Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>{product.origin.trim()}</Text>
                </View>
              )}

              {/* Weight */}
              {product.weight && product.weight > 0 && (
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: colors.tabIconDefault }]}>Weight:</Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>{product.weight} kg</Text>
                </View>
              )}

              {/* Dimensions */}
              {product.dimensions && product.dimensions.trim() && (
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: colors.tabIconDefault }]}>Dimensions:</Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>{product.dimensions.trim()}</Text>
                </View>
              )}

              {/* Quantity Available */}
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: colors.tabIconDefault }]}>Available Quantity:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>{product.quantity}</Text>
              </View>

            </View>
          </View>

          {/* Care Instructions */}
          {product.care && product.care.trim() && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Care Instructions</Text>
              <Text style={[styles.careText, { color: colors.text }]}>
                {product.care.trim()}
              </Text>
            </View>
          )}

          {/* Reviews Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Customer Reviews</Text>
            {reviewsLoading ? (
              <View style={styles.reviewsPlaceholder}>
                <ActivityIndicator size="small" color={colors.primary} />
                <Text style={[styles.reviewsText, { color: colors.tabIconDefault }]}>
                  Loading reviews...
                </Text>
              </View>
            ) : (
              <ReviewList
                reviews={reviews}
                onAddReview={() => setShowReviewModal(true)}
                showAddButton={true}
              />
            )}
          </View>

          {/* Add to Cart Section */}
          <View style={styles.actionsContainer}>
            <View style={[styles.quantityContainer, { borderColor: colors.border }]}>
              <TouchableOpacity
                style={[styles.quantityButton, { borderRightColor: colors.border }]}
                onPress={handleDecrement}
                disabled={quantity <= 1}
              >
                <Ionicons
                  name="remove"
                  size={20}
                  color={quantity <= 1 ? colors.tabIconDefault : colors.text}
                />
              </TouchableOpacity>

              <Text style={[styles.quantity, { color: colors.text }]}>
                {quantity}
              </Text>

              <TouchableOpacity
                style={[styles.quantityButton, { borderLeftColor: colors.border }]}
                onPress={handleIncrement}
              >
                <Ionicons name="add" size={20} color={colors.text} />
              </TouchableOpacity>
            </View>

            <Button
              title={hasSelectableOptions() ? "Select Options" : "Add to Cart"}
              style={styles.addToCartButton}
              onPress={handleAddToCartClick}
              disabled={product.stockLevel <= 0}
            />
          </View>
        </View>
      </ScrollView>

      {/* Product Selection Modal */}
      {hasSelectableOptions() && (
        <ProductSelectionModal
          visible={showSelectionModal}
          onClose={() => setShowSelectionModal(false)}
          product={{
            id: product.id.toString(),
            name: product.name,
            price: product.price,
            discountPrice: product.discountPrice,
            imageUrl: product.imageUrl,
            sizes: product.sizes,
            colors: product.colors
          }}
          onSuccess={() => {
            setShowSelectionModal(false);
            // The ProductSelectionModal handles adding to cart
          }}
        />
      )}

      {/* Review Modal */}
      <ReviewModal
        visible={showReviewModal}
        onClose={() => setShowReviewModal(false)}
        productId={product.id}
        productName={product.name}
        onSuccess={handleReviewSuccess}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  imageContainer: {
    width: '100%',
  },
  mainImage: {
    width: '100%',
    height: 300,
  },
  thumbnailsContainer: {
    flexDirection: 'row',
    padding: Layout.spacing.md,
    justifyContent: 'center',
  },
  thumbnailButton: {
    width: 60,
    height: 60,
    borderRadius: Layout.borderRadius.sm,
    marginHorizontal: Layout.spacing.xs,
    borderWidth: 2,
    borderColor: 'transparent',
    overflow: 'hidden',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  infoContainer: {
    padding: Layout.spacing.md,
  },
  productName: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.sm,
  },
  brandCategoryContainer: {
    marginBottom: Layout.spacing.sm,
  },
  brandText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  categoryText: {
    fontSize: 14,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  starsContainer: {
    flexDirection: 'row',
  },
  starIcon: {
    marginRight: 2,
  },
  reviewCount: {
    fontSize: 14,
    marginLeft: Layout.spacing.xs,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  discountPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    marginRight: Layout.spacing.sm,
  },
  originalPrice: {
    fontSize: 18,
    textDecorationLine: 'line-through',
    marginRight: Layout.spacing.sm,
  },
  discountBadge: {
    paddingHorizontal: Layout.spacing.xs,
    paddingVertical: 2,
    borderRadius: Layout.borderRadius.sm,
  },
  discountBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  freeShippingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.sm,
    marginBottom: Layout.spacing.sm,
    alignSelf: 'flex-start',
  },
  freeShippingText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  stockText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: Layout.spacing.xs,
  },
  section: {
    marginBottom: Layout.spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: Layout.spacing.sm,
  },
  description: {
    fontSize: 14,
    lineHeight: 22,
  },
  sizesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Layout.spacing.xs,
  },
  sizeChip: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.sm,
    marginRight: Layout.spacing.xs,
    marginBottom: Layout.spacing.xs,
  },
  sizeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  colorsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Layout.spacing.xs,
  },
  colorChip: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.sm,
    marginRight: Layout.spacing.xs,
    marginBottom: Layout.spacing.xs,
  },
  colorText: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectionHint: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: Layout.spacing.xs,
    fontStyle: 'italic',
  },
  featuresList: {
    gap: Layout.spacing.xs,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureText: {
    fontSize: 14,
    marginLeft: Layout.spacing.xs,
    flex: 1,
  },
  detailsContainer: {
    gap: Layout.spacing.xs,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Layout.spacing.xs,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    flex: 2,
    textAlign: 'right',
  },
  careText: {
    fontSize: 14,
    lineHeight: 20,
  },
  reviewsPlaceholder: {
    padding: Layout.spacing.md,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: Layout.borderRadius.md,
    borderStyle: 'dashed',
  },
  reviewsText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: Layout.spacing.xs,
  },
  reviewsSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Layout.spacing.md,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    overflow: 'hidden',
    marginRight: Layout.spacing.md,
  },
  quantityButton: {
    padding: Layout.spacing.sm,
    borderRightWidth: 1,
    borderLeftWidth: 1,
  },
  quantity: {
    paddingHorizontal: Layout.spacing.md,
    fontSize: 16,
    fontWeight: '500',
  },
  addToCartButton: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
  },
  loadingText: {
    fontSize: 16,
    marginTop: Layout.spacing.md,
  },
  notFoundContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
  },
  notFoundText: {
    fontSize: 18,
    fontWeight: '500',
    marginVertical: Layout.spacing.md,
  },
  notFoundButton: {
    marginTop: Layout.spacing.md,
  },
});
