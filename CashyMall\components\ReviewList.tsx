import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  useColorScheme,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';
import { Review } from '../types/product';

interface ReviewListProps {
  reviews: Review[];
  onAddReview?: () => void;
  showAddButton?: boolean;
}

export default function ReviewList({ 
  reviews, 
  onAddReview, 
  showAddButton = true 
}: ReviewListProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderStars = (rating: number) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <Ionicons
            key={star}
            name={star <= rating ? "star" : "star-outline"}
            size={14}
            color="#FFD700"
            style={styles.starIcon}
          />
        ))}
      </View>
    );
  };

  if (reviews.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="chatbubble-outline" size={48} color={colors.tabIconDefault} />
        <Text style={[styles.emptyTitle, { color: colors.text }]}>
          No reviews yet
        </Text>
        <Text style={[styles.emptySubtitle, { color: colors.tabIconDefault }]}>
          Be the first to review this product
        </Text>
        {showAddButton && onAddReview && (
          <TouchableOpacity
            style={[styles.addReviewButton, { borderColor: colors.primary }]}
            onPress={onAddReview}
          >
            <Ionicons name="add" size={20} color={colors.primary} />
            <Text style={[styles.addReviewText, { color: colors.primary }]}>
              Write a Review
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Add Review Button */}
      {showAddButton && onAddReview && (
        <TouchableOpacity
          style={[styles.addReviewButton, { borderColor: colors.primary }]}
          onPress={onAddReview}
        >
          <Ionicons name="add" size={20} color={colors.primary} />
          <Text style={[styles.addReviewText, { color: colors.primary }]}>
            Write a Review
          </Text>
        </TouchableOpacity>
      )}

      {/* Reviews List */}
      {reviews.map((review) => (
        <View key={review.id} style={[styles.reviewItem, { borderBottomColor: colors.border }]}>
          {/* Review Header */}
          <View style={styles.reviewHeader}>
            <View style={styles.userInfo}>
              <View style={[styles.avatar, { backgroundColor: colors.primary }]}>
                <Text style={styles.avatarText}>
                  {review.userName.charAt(0).toUpperCase()}
                </Text>
              </View>
              <View style={styles.userDetails}>
                <View style={styles.nameRatingRow}>
                  <Text style={[styles.userName, { color: colors.text }]}>
                    {review.userName}
                  </Text>
                  {review.verified && (
                    <View style={[styles.verifiedBadge, { backgroundColor: colors.primary }]}>
                      <Ionicons name="checkmark" size={12} color="#ffffff" />
                      <Text style={styles.verifiedText}>Verified</Text>
                    </View>
                  )}
                </View>
                {renderStars(review.rating)}
              </View>
            </View>
            <Text style={[styles.reviewDate, { color: colors.tabIconDefault }]}>
              {formatDate(review.createdAt)}
            </Text>
          </View>

          {/* Review Content */}
          <View style={styles.reviewContent}>
            <Text style={[styles.reviewTitle, { color: colors.text }]}>
              {review.title}
            </Text>
            <Text style={[styles.reviewComment, { color: colors.text }]}>
              {review.comment}
            </Text>
          </View>

          {/* Review Footer */}
          {review.helpful > 0 && (
            <View style={styles.reviewFooter}>
              <TouchableOpacity style={styles.helpfulButton}>
                <Ionicons name="thumbs-up-outline" size={16} color={colors.tabIconDefault} />
                <Text style={[styles.helpfulText, { color: colors.tabIconDefault }]}>
                  Helpful ({review.helpful})
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: Layout.spacing.xl,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.xs,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: Layout.spacing.lg,
  },
  addReviewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.md,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.md,
  },
  addReviewText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: Layout.spacing.xs,
  },
  reviewItem: {
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Layout.spacing.sm,
  },
  userInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Layout.spacing.sm,
  },
  avatarText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  userDetails: {
    flex: 1,
  },
  nameRatingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: Layout.spacing.xs,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  verifiedText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '500',
    marginLeft: 2,
  },
  starsContainer: {
    flexDirection: 'row',
  },
  starIcon: {
    marginRight: 1,
  },
  reviewDate: {
    fontSize: 12,
  },
  reviewContent: {
    marginBottom: Layout.spacing.sm,
  },
  reviewTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: Layout.spacing.xs,
  },
  reviewComment: {
    fontSize: 14,
    lineHeight: 20,
  },
  reviewFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  helpfulButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  helpfulText: {
    fontSize: 12,
    marginLeft: Layout.spacing.xs,
  },
});
